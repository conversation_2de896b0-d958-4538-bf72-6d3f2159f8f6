<html>
  <head>
    <title>Chess Openings Trainer</title>

    <link rel="shortcut icon" href="/static/favicon.ico">

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>

    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>

    <script src="/static/js/chess.js"></script>

    <link rel="stylesheet" href="/static/css/chessboard-1.0.0.min.css">
    <script src="/static/js/chessboard-1.0.0.min.js"></script>

    <style>
      .trainer-controls {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
      }
      .start-training-btn {
        font-size: 1.2rem;
        font-weight: bold;
        padding: 15px 30px;
        border-radius: 8px;
        width: 100%;
        margin-top: 20px;
      }
      .orientation-toggle {
        display: flex;
        align-items: center;
        gap: 10px;
      }
      .control-group {
        margin-bottom: 15px;
      }
      .control-label {
        font-weight: 600;
        margin-bottom: 5px;
        color: #495057;
      }
    </style>

  </head>
  <body>
    <div class="container-fluid">
      <div class="row justify-content-center">
        <div class="col-12 col-lg-10 col-xl-8">
          <div class="card mt-3">
            <div class="card-header bg-primary text-white">
              <h3 class="mb-0 text-center">Chess Openings Trainer</h3>
            </div>
            <div class="card-body">
              <div class="row">
                <!-- Chess Board Column -->
                <div class="col-md-6">
                  <div id="chess_board" class="mx-auto mb-3" style="width: 400px;"></div>
                  <div class="text-center">
                    <strong><div id="status" class="mb-3"></div></strong>
                  </div>
                </div>

                <!-- Controls Column -->
                <div class="col-md-6">
                  <!-- Training Settings (shown when not training) -->
                  <div id="training-settings" class="trainer-controls">
                    <h5 class="mb-3 text-center">Training Settings</h5>

                    <!-- Board Orientation Toggle -->
                    <div class="control-group">
                      <div class="control-label">Board Orientation</div>
                      <div class="orientation-toggle">
                        <span>Play as:</span>
                        <div class="btn-group btn-group-toggle" data-toggle="buttons">
                          <label class="btn btn-outline-primary active">
                            <input type="radio" name="orientation" id="white_orientation" value="white" checked> White
                          </label>
                          <label class="btn btn-outline-primary">
                            <input type="radio" name="orientation" id="black_orientation" value="black"> Black
                          </label>
                        </div>
                      </div>
                    </div>

                    <!-- FEN Input -->
                    <div class="control-group">
                      <div class="control-label">Starting Position (FEN)</div>
                      <div class="input-group">
                        <input id="fen" type="text" class="form-control" placeholder="Enter FEN or leave empty for starting position">
                        <div class="input-group-append">
                          <button id="set_fen" class="btn btn-outline-success">Set Position</button>
                        </div>
                      </div>
                    </div>

                    <!-- Move Time -->
                    <div class="control-group">
                      <div class="control-label">Move Time</div>
                      <select id="move_time" class="form-control">
                        <option value="instant" selected>Instant response</option>
                        <option value="1">1 second</option>
                        <option value="2">2 seconds</option>
                        <option value="3">3 seconds</option>
                        <option value="4">4 seconds</option>
                        <option value="5">5 seconds</option>
                        <option value="6">6 seconds</option>
                        <option value="7">7 seconds</option>
                        <option value="8">8 seconds</option>
                        <option value="9">9 seconds</option>
                        <option value="10">10 seconds</option>
                      </select>
                    </div>

                    <!-- Engine Selection -->
                    <div class="control-group">
                      <div class="control-label">Chess Engine</div>
                      <select id="engine" class="form-control">
                        <option value="stockfish" selected>Stockfish</option>
                        <option value="lczero">Leela Chess Zero (LC0)</option>
                      </select>
                    </div>

                    <!-- Start Training Button -->
                    <button id="start_training" class="btn btn-success start-training-btn">
                      🚀 Start Training
                    </button>
                  </div>

                  <!-- Training Session (shown when training) -->
                  <div id="training-session" class="trainer-controls" style="display: none;">
                    <h5 class="mb-3 text-center">Training Session</h5>

                    <!-- Training Stats -->
                    <div class="row mb-3">
                      <div class="col-6">
                        <div class="text-center">
                          <div class="control-label">Remaining Time</div>
                          <div id="remaining-time" class="h4 text-primary">5:00</div>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="text-center">
                          <div class="control-label">Moves Passed</div>
                          <div id="moves-passed" class="h4 text-success">0</div>
                        </div>
                      </div>
                    </div>

                    <!-- Training Info -->
                    <div class="mb-3">
                      <div class="small text-muted">
                        <div>Playing as: <span id="session-orientation" class="font-weight-bold"></span></div>
                        <div>Engine: <span id="session-engine" class="font-weight-bold"></span></div>
                        <div>Move Time: <span id="session-move-time" class="font-weight-bold"></span></div>
                      </div>
                    </div>

                    <!-- Surrender Button -->
                    <button id="surrender" class="btn btn-danger start-training-btn">
                      🏳️ Surrender
                    </button>
                  </div>

                  <!-- Additional Controls -->
                  <div class="mt-3">
                    <div class="btn-group w-100" role="group">
                      <button id="new_game" class="btn btn-outline-secondary">New Game</button>
                      <button id="take_back" class="btn btn-outline-warning">Take Back</button>
                      <button id="flip_board" class="btn btn-outline-info">Flip Board</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>

<script>
  // make computer move
  function make_move() {
    // disable take back button
    $('#take_back').attr('disabled', true);

    // Use training parameters if training is active, otherwise use current UI values
    var moveTime = trainingActive ? trainingParams.moveTime : $('#move_time option:selected').val();
    var engineType = trainingActive ? trainingParams.engine : $('#engine option:selected').val();
    var orientation = trainingActive ? trainingParams.orientation : board.orientation();

    // make HTTP POST request to make move API
    $.post('/make_move',{
        'pgn': game.pgn(),
        'move_time': moveTime,
        'engine_type': engineType,
        'orientation': orientation
      }, function(data) {
        // load fen into the current board state
        game.move(data.best_move, { sloppy: true })

        // update board position
        board.position(game.fen());

        // update game status
        updateStatus();

        // re-enable take back button
        $('#take_back').attr('disabled', false);

        // if training is active, check continue flag
        if (trainingActive) {
          if (data.continue === false) {
            stopTraining('Training completed! You made an incorrect move or the opening sequence ended.');
          } else {
            // Increment moves passed counter
            movesPassed++;
            $('#moves-passed').text(movesPassed);
          }
        }
    }).fail(function() {
        // re-enable take back button on error
        $('#take_back').attr('disabled', false);
        if (trainingActive) {
          stopTraining('Error occurred during training.');
        }
    });
  }

  // training state
  var trainingActive = false;
  var trainingParams = {};
  var trainingTimer = null;
  var remainingTime = 300; // 5 minutes in seconds
  var movesPassed = 0;

  // timer functions
  function startTimer() {
    trainingTimer = setInterval(function() {
      remainingTime--;
      updateTimerDisplay();

      if (remainingTime <= 0) {
        stopTraining('Time is up!');
      }
    }, 1000);
  }

  function stopTimer() {
    if (trainingTimer) {
      clearInterval(trainingTimer);
      trainingTimer = null;
    }
  }

  function updateTimerDisplay() {
    var minutes = Math.floor(remainingTime / 60);
    var seconds = remainingTime % 60;
    var timeString = minutes + ':' + (seconds < 10 ? '0' : '') + seconds;
    $('#remaining-time').text(timeString);

    // Change color when time is running low
    if (remainingTime <= 60) {
      $('#remaining-time').removeClass('text-primary').addClass('text-danger');
    } else if (remainingTime <= 120) {
      $('#remaining-time').removeClass('text-primary').addClass('text-warning');
    }
  }

  function resetTimer() {
    remainingTime = 300;
    updateTimerDisplay();
    $('#remaining-time').removeClass('text-warning text-danger').addClass('text-primary');
  }

  function stopTraining(message) {
    trainingActive = false;
    stopTimer();

    // Show training settings, hide session
    $('#training-settings').show();
    $('#training-session').hide();

    // Reset button
    $('#start_training').removeClass('btn-danger').addClass('btn-success');
    $('#start_training').html('🚀 Start Training');

    // Re-enable take back button
    $('#take_back').attr('disabled', false);

    if (message) {
      alert(message);
    }
  }

  // handle orientation toggle
  $('input[name="orientation"]').on('change', function() {
    var selectedOrientation = $('input[name="orientation"]:checked').val();
    if (selectedOrientation === 'white') {
      board.orientation('white');
    } else {
      board.orientation('black');
    }
  });

  // handle surrender button click
  $('#surrender').on('click', function() {
    stopTraining('Training session ended by surrender.');
  });

  // handle start training button click
  $('#start_training').on('click', function() {
    // Validate that it's the bot's turn to move
    var isPlayerWhite = selectedOrientation === 'white';
    var isWhiteToMove = game.turn() === 'w';
    var isPlayerTurn = isPlayerWhite === isWhiteToMove;

    if (!isPlayerTurn) {
      alert('Training cannot start - it\'s your turn to move! Please set up a position where the engine should move first.');
      stopTraining();
      return;
    }
    if (!trainingActive) {
      // Save training parameters
      var selectedOrientation = $('input[name="orientation"]:checked').val();
      var fenValue = $('#fen').val().trim();
      var moveTime = $('#move_time option:selected').val();
      var engine = $('#engine option:selected').val();

      trainingParams = {
        orientation: selectedOrientation,
        startingFen: fenValue || 'start',
        moveTime: moveTime,
        engine: engine,
        startingPosition: game.fen()
      };

      // Start training
      trainingActive = true;
      movesPassed = 0;
      resetTimer();

      // Hide settings, show session
      $('#training-settings').hide();
      $('#training-session').show();

      // Update session info
      $('#session-orientation').text(selectedOrientation.charAt(0).toUpperCase() + selectedOrientation.slice(1));
      $('#session-engine').text(engine === 'stockfish' ? 'Stockfish' : 'Leela Chess Zero');
      $('#session-move-time').text(moveTime === 'instant' ? 'Instant' : moveTime + ' second' + (moveTime === '1' ? '' : 's'));
      $('#moves-passed').text(movesPassed);

      // Set board orientation
      board.orientation(selectedOrientation);

      updateStatus();
      startTimer();

      // Disable take back during training
      $('#take_back').attr('disabled', true);

      // Bot always makes the first move in training
      make_move();
    } else {
      // Stop training
      stopTraining();
    }
  });

  // handle new game button click
  $('#new_game').on('click', function() {
    // reset board state
    game.reset();

    // set initial board position
    board.position('start');

    // stop training if active
    if (trainingActive) {
      stopTraining();
    }

    updateStatus();
  });

  // handle take back button click
  $('#take_back').on('click', function() {
    // take move back
    game.undo();
    game.undo();

    // update board position
    board.position(game.fen());

    // update game status
    updateStatus();
  });

  // handle flip board button click
  $('#flip_board').on('click', function() {
    // flip board
    board.flip();

    // update orientation radio buttons
    var currentOrientation = board.orientation();
    if (currentOrientation === 'white') {
      $('#black_orientation').prop('checked', true).parent().addClass('active');
      $('#white_orientation').prop('checked', false).parent().removeClass('active');
    } else {
      $('#white_orientation').prop('checked', true).parent().addClass('active');
      $('#black_orientation').prop('checked', false).parent().removeClass('active');
    }
  });
  
  // handle set FEN button click
  $('#set_fen').on('click', function() {
    // set user FEN
    var fenValue = $('#fen').val().trim();

    if (fenValue === '') {
      // Empty FEN means starting position
      game.reset();
      board.position('start');
      updateStatus();
    } else {
      // FEN parsed
      if (game.load(fenValue)) {
        // set board position
        board.position(game.fen());
        updateStatus();
      } else {
        // FEN is not parsed
        alert('Invalid FEN position!');
      }
    }
  });

  // GUI board & game state variables
  var board = null;
  var game = new Chess();
  var $status = $('#status');
  var $fen = $('#fen');

  // on picking up a piece
  function onDragStart (source, piece, position, orientation) {
    // do not pick up pieces if the game is over
    if (game.game_over()) return false

    // only pick up pieces for the side to move
    if ((game.turn() === 'w' && piece.search(/^b/) !== -1) ||
        (game.turn() === 'b' && piece.search(/^w/) !== -1)) {
      return false
    }
  }

  // on dropping piece
  function onDrop (source, target) {
    // see if the move is legal
    var move = game.move({
      from: source,
      to: target,
      promotion: 'q' // NOTE: always promote to a queen for example simplicity
    })

    // illegal move
    if (move === null) return 'snapback'

    // update game status
    updateStatus();

    // if training is active and game is not over, make computer move
    if (trainingActive && !game.game_over()) {
      setTimeout(make_move, 300); // Small delay for better UX
    }
  }

  // update the board position after the piece snap
  // for castling, en passant, pawn promotion
  function onSnapEnd () {
    board.position(game.fen())
  }

  // update game status
  function updateStatus () {
    var status = ''

    var moveColor = 'White'
    if (game.turn() === 'b') {
      moveColor = 'Black'
    }

    // checkmate?
    if (game.in_checkmate()) {
      status = 'Game over, ' + moveColor + ' is in checkmate.'
    }

    // draw?
    else if (game.in_draw()) {
      status = 'Game over, drawn position'
    }

    // game still on
    else {
      status = moveColor + ' to move'

      // check?
      if (game.in_check()) {
        status += ', ' + moveColor + ' is in check'
      }
    }

    // update DOM elements
    $status.html(status)
    $fen.val(game.fen())
  }

  // chess board configuration
  var config = {
    draggable: true,
    position: 'start',
    onDragStart: onDragStart,
    onDrop: onDrop,
    onSnapEnd: onSnapEnd
  }
  
  // create chess board widget instance
  board = Chessboard('chess_board', config)
  
  // prevent scrolling on touch devices
  $('#chess_board').on('scroll touchmove touchend touchstart contextmenu', function(e) {
    e.preventDefault();
  });

  // update game status
  updateStatus();
</script>
